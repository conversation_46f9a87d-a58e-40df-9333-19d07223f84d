<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Save extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Запазване на продукт
     */
    public function execute() {
        $json = [];

        ob_start();

        // Проверка за валидност на заявката
        if ($this->request->server['REQUEST_METHOD'] != 'POST') {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }

        $post = $this->requestPost();
 
        // Валидация на задължителни полета
        if (!isset($post['product_description']) || !is_array($post['product_description'])) {
            $json['error'] = 'Липсва описание на продукта';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/product' => 'productModel',
            'tool/image' => 'imageModel'
        ]);
        
    
        // Подготвяне на данните за продукта
        $data = [
            'model' => $post['model'] ?? '',
            'sku' => $post['sku'] ?? '',
            'upc' => $post['upc'] ?? '',
            'ean' => $post['ean'] ?? '',
            'jan' => $post['jan'] ?? '',
            'isbn' => $post['isbn'] ?? '',
            'mpn' => $post['mpn'] ?? '',
            'location' => $post['location'] ?? '',
            'price' => $post['price'] ?? 0,
            'tax_class_id' => $post['tax_class_id'] ?? 0,
            'quantity' => $post['quantity'] ?? 0,
            'minimum' => $post['minimum'] ?? 1,
            'subtract' => $post['subtract'] ?? 1,
            'stock_status_id' => $post['stock_status_id'] ?? ($post['quantity'] > 0 ? 7 : 5),
            'shipping' => $post['shipping'] ?? 1,
            'date_available' => $post['date_available'] ?? date('Y-m-d'),
            'length' => $post['length'] ?? 0,
            'width' => $post['width'] ?? 0,
            'height' => $post['height'] ?? 0,
            'length_class_id' => $post['length_class_id'] ?? 1,
            'weight' => $post['weight'] ?? 0,
            'weight_class_id' => $post['weight_class_id'] ?? 1,
            'status' => $post['status'] ?? 0,
            'shipping' => $post['shipping'] ?? 1,
            'sort_order' => $post['sort_order'] ?? 0,
            'manufacturer_id' => $post['manufacturer_id'] ?? 0,
            'product_description' => $post['product_description'],
            'product_category' => $post['product_category'] ?? [],
            'image' => '',
            'product_image' => [],
            'subtract' => $post['subtract'] ?? 1,
            'product_related' => $post['product_related'] ?? [],
            'product_special' => $post['product_special'] ?? [],
            'product_attribute' => $post['product_attribute'] ?? [],
            'product_option' => $post['product_option'] ?? [],
            'product_seo_url' => $this->processProductSeoURL($post['product_seo_url'])
        ];
        
        // Обработка на основното изображение
        if (!empty($this->requestPost('main_image'))) {
            $main_image = $this->requestPost('main_image');
        }
        elseif (empty($this->requestPost('main_image')) && !empty($post['product_image'])) {
            $main_image = $post['product_image'][0]['image'];
            array_shift($post['product_image']);
        }
        else {
            $main_image = '';
        }

        $main_image_file = ThemeData()->getImageServerPath() . $main_image;

        if (file_exists($main_image_file)) {
            $data['image'] = ltrim($main_image, '/');
        } else {
            $data['image'] = '';
        }


        // Обработка на допълнителните изображения
        $product_images = [];
        if (!empty($post['product_image'])) {
            $sort_order = 0;
            foreach ($post['product_image'] as $key => $image) {

                $file = ThemeData()->getImageServerPath() . $image;
                if (file_exists($file)) {
                    $product_images[] = [
                        'image' => ltrim($image, '/'),
                        'sort_order' => null !== $post['product_image_sort_order'] ? (int)$post['product_image_sort_order'][$key] : $sort_order++
                    ];
                }
            }
        }
        $data['product_image'] = $product_images;

        $data['product_option'] = $this->processProductOptions($post['product_option']);
        
        // Запазване на продукта
        $product_id = $post['product_id'] ?? 0;

        if ($product_id) {
            $this->productModel->editProduct($product_id, $data);
            $json['success'] = 'Продуктът беше успешно актуализиран';
        } else {
            $product_id = $this->productModel->addProduct($data);
            $json['success'] = 'Продуктът беше успешно добавен';
        }
        
        // Добавяне на ID на продукта в отговора
        $json['product_id'] = $product_id;
        
        // Добавяне на URL за пренасочване
        $json['redirect'] = $this->getAdminLink('catalog/product/edit', 'product_id=' . $product_id, true);
        
        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);
    }

    private function processProductOptions($product_options) {
        $processed_options = [];
        foreach ($product_options as $option) {
            $processed_option = [
                'product_option_id' => $option['product_option_id'] ?? 0,
                'option_id' => $option['option_id'] ?? 0,
                'name' => $option['name'] ?? '',
                'type' => $option['type'] ?? '',
                'value' => $option['value'] ?? '',
                'required' => $option['required'] ?? 0,
                'product_option_value' => []
            ];

            if (isset($option['product_option_value'])) {
                foreach ($option['product_option_value'] as $option_value) {
                    $processed_option['product_option_value'][] = [
                        'product_option_value_id' => $option_value['product_option_value_id'] ?? 0,
                        'option_value_id' => $option_value['option_value_id'] ?? 0,
                        'quantity' => $option_value['quantity'] ?? 0,
                        'subtract' => $option_value['subtract'] ?? 0,
                        'price' => $option_value['price'] ?? 0,
                        'price_prefix' => $option_value['price_prefix'] ?? '+',
                        'points' => $option_value['points'] ?? 0,
                        'points_prefix' => $option_value['points_prefix'] ?? '+',
                        'weight' => $option_value['weight'] ?? 0,
                        'weight_prefix' => $option_value['weight_prefix'] ?? '+'
                    ];
                }
            }

            $processed_options[] = $processed_option;
        }

        return $processed_options;
    }

    private function processProductSeoURL($product_seo_url) {
        $processed_seo_url = [0 => []];
        foreach ($product_seo_url as $language_id => $seo_url) {
            $processed_seo_url[0][$language_id] = $seo_url;
        }
        return $processed_seo_url;
    }

}