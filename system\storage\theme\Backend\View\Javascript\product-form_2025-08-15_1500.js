/**
 * JavaScript функционалност за страницата за редактиране на продукти
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за продуктовата форма
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initProductForm();
        }
    });

    // Добавяне на функционалност за продуктовата форма към основния модул
    if (typeof BackendModule !== 'undefined') {
        // Ensure config object exists
        BackendModule.config = BackendModule.config || {};

        // Extract user_token from URL and store it
        try {
            const params = new URLSearchParams(window.location.search);
            BackendModule.config.userToken = params.get('user_token') || ''; // Default to empty string if not found
        } catch (e) {
            console.error('Error parsing URL params for user_token:', e);
            BackendModule.config.userToken = ''; // Fallback in case of error
        }

        // Get active language ID from the page (set by backend)
        BackendModule.config.activeLanguageId = window.activeLanguageId || 2; // Default to 2 if not set

        // Добавяне на методи към основния модул
        Object.assign(BackendModule, {
            /**
             * Инициализация на функционалността за продуктовата форма
             */
            
            
            initProductForm: function() {
                this.initTabs();
                this.initImageUpload();
                this.initProductImageActions(); // Преименувана от initAdditionalImages
                this.initCategoryAutocomplete();
                this.initBrandAutocomplete();
                this.initAttributeAutocomplete();
                this.initAttributes();
                this.initOptions();
                this.initOptionValueFiltering();
                this.initProductStatus();
                this.initLanguageTabs();
                this.initRelatedProducts();
                this.initSpecialPricesManagement();
                this.initFormSubmission(); // Инициализираме обработката на формата
            },

            /**
             * Инициализация на табовете
             */
            initTabs: function() {
                const tabButtons = document.querySelectorAll('.tab-button');
                const tabContents = document.querySelectorAll('.tab-content');
                
                // Активиране на таб от URL хеша, ако е наличен
                if (window.location.hash) {
                    const tabId = window.location.hash.substring(1);
                    const tabButton = document.querySelector(`[data-tab="${tabId}"]`);
                    if (tabButton) {
                        tabButtons.forEach(btn => btn.classList.remove('active', 'text-primary', 'border-b-2', 'border-primary') || btn.classList.add('text-gray-500'));
                        tabButton.classList.add('active', 'text-primary', 'border-b-2', 'border-primary');
                        tabButton.classList.remove('text-gray-500');
                        
                        tabContents.forEach(content => content.classList.add('hidden'));
                        const activeTab = document.getElementById(tabId);
                        if (activeTab) {
                            activeTab.classList.remove('hidden');
                        }
                    }
                }
                
                // Превключване между табовете
                tabButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const tabId = this.getAttribute('data-tab');
                        
                        // Активиране на бутона
                        tabButtons.forEach(btn => {
                            btn.classList.remove('active', 'text-primary', 'border-b-2', 'border-primary');
                            btn.classList.add('text-gray-500');
                        });
                        this.classList.add('active', 'text-primary', 'border-b-2', 'border-primary');
                        this.classList.remove('text-gray-500');
                        
                        // Показване на съдържанието
                        tabContents.forEach(content => content.classList.add('hidden'));
                        document.getElementById(tabId).classList.remove('hidden');
                        
                        // Обновяване на URL хеша
                        window.location.hash = tabId;
                    });
                });
            },

            /**
             * Инициализация на качването на основно изображение
             */
            initImageUpload: function() {
                const buttonUpload = document.getElementById('button-upload');

                if (buttonUpload) {
                    buttonUpload.addEventListener('click', () => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*';

                        input.onchange = (e) => {
                            const file = e.target.files[0];
                            if (file) {
                                // Проверяваме дали image-manager.js е зареден
                                if (!this.uploadSingleFile || typeof this.uploadSingleFile !== 'function') {
                                    console.error('image-manager.js не е зареден или uploadSingleFile методът не е наличен');
                                    this.showAlert('error', 'Функционалността за качване не е налична');
                                    return;
                                }

                                // Използваме uploadSingleFile метода от image-manager.js
                                this.uploadSingleFile(file)
                                    .then(result => {
                                        if (result && result.success) {
                                            // Обновяваме UI за основното изображение
                                            const imagePreview = document.getElementById('image-preview');
                                            const imageInput = document.getElementById('image');

                                            if (imagePreview) {
                                                imagePreview.src = result.thumb;
                                            }
                                            if (imageInput) {
                                                imageInput.value = result.filename;
                                            }

                                            this.showAlert('success', 'Изображението е качено успешно!');
                                        } else {
                                            this.showAlert('error', 'Грешка при качване на изображение');
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Грешка при качване на изображение:', error);
                                        this.showAlert('error', 'Възникна грешка при качване на изображението');
                                    });
                            }
                        };

                        input.click();
                    });
                }
            },

            /**
             * Инициализация на действията, свързани с изображенията в продукта.
             * Настройва event listeners за бутоните за качване и избор от библиотека.
             */
            initProductImageActions: function() {
                const imageTab = document.getElementById('tab-images');
                if (!imageTab) return;

                // Drag & Drop за качване
                const dropArea = imageTab.querySelector('.image-upload-area');
                if (dropArea) {
                    dropArea.addEventListener('dragover', (e) => { e.preventDefault(); e.stopPropagation(); dropArea.classList.add('border-primary'); });
                    dropArea.addEventListener('dragleave', (e) => { e.preventDefault(); e.stopPropagation(); dropArea.classList.remove('border-primary'); });
                    dropArea.addEventListener('drop', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        dropArea.classList.remove('border-primary');
                        const files = Array.from(e.dataTransfer.files);
                        if (files.length > 0) this.uploadAndAddImages(files);
                    });
                }
                
                // Бутон "Изберете файлове"
                const fileInput = imageTab.querySelector('input[type="file"][name="image"]');
                if (fileInput) {
                    fileInput.addEventListener('change', (e) => {
                        const files = Array.from(e.target.files);
                        if (files.length > 0) this.uploadAndAddImages(files);
                        e.target.value = ''; // Изчистване на полето
                    });
                }

                // Бутон за добавяне на нова снимка
                const addImageBtn = document.getElementById('additional-image-upload');
                if (addImageBtn) {
                    addImageBtn.addEventListener('change', (e) => {
                        const files = Array.from(e.target.files);
                        if (files.length > 0) this.uploadAndAddImages(files);
                        e.target.value = ''; // Изчистване на полето
                    });
                }

                // Глобален event listener за таба с изображения
                imageTab.addEventListener('click', (e) => {
                    const button = e.target.closest('button');
                    if (!button) return;

                    const action = button.dataset.action || button.id;
                    const imageContainer = button.closest('.relative.group');

                    switch (action) {
                        case 'open-image-library': // Главен бутон "Избери от библиотеката"
                        case 'open-additional-image-library': // Бутон "+" за избор от библиотека
                            e.preventDefault();
                            this.openLibraryForMultipleImages();
                            break;
                        
                        case 'select': // Бутон "Избери от библиотеката" за конкретно изображение (замяна)
                            e.preventDefault();
                            this.openLibraryForSingleImage(imageContainer);
                            break;
                        
                        case 'remove': // Премахване на изображение
                             if (imageContainer && confirm('Сигурни ли сте, че искате да премахнете това изображение?')) {
                                imageContainer.remove();
                                this.showAlert('success', 'Изображението е премахнато!');
                            }
                            break;
                    }
                });
            },

            /**
             * Отваря мениджъра за избор на много изображения
             */
            openLibraryForMultipleImages: function() {


                console.log('openLibraryForMultipleImages');

                BackendModule.openImageManager({
                    singleSelection: false,
                    startDirectory: this.getLastImageDirectory(),
                    callback: (selectedImages) => {
                        if (selectedImages && selectedImages.length > 0) {
                            selectedImages.forEach(image => {
                                this.addImageToProductForm(image.path, image.thumb);
                            });
                            this.showAlert('success', `Успешно добавени ${selectedImages.length} изображения.`);
                        }
                    }
                });
            },

            /**
             * Отваря мениджъра за замяна на единично изображение
             * @param {HTMLElement} targetContainer - Контейнерът на изображението, което ще се заменя
             */
            openLibraryForSingleImage: function(targetContainer) {
                BackendModule.openImageManager({
                    singleSelection: true,
                    startDirectory: this.getLastImageDirectory(),
                    target: targetContainer,
                    callback: (selectedImages, target) => {
                        if (selectedImages && selectedImages.length > 0 && target) {
                            const image = selectedImages[0];
                            const imgElement = target.querySelector('img');
                            const inputElement = target.querySelector('input[type="hidden"]');
                            
                            if (imgElement) imgElement.src = image.thumb;
                            if (inputElement) inputElement.value = image.path;
                            
                            this.showAlert('success', 'Изображението е успешно заменено.');
                        }
                    }
                });
            },

            /**
             * Качва файлове и ги добавя към формата
             * @param {File[]} files - Масив от файлове за качване
             */
            uploadAndAddImages: function(files) {
                if (!files || files.length === 0) return;

                // Проверяваме дали image-manager.js е зареден
                if (!this.uploadFiles || typeof this.uploadFiles !== 'function') {
                    console.error('image-manager.js не е зареден или uploadFiles методът не е наличен');
                    this.showAlert('error', 'Функционалността за качване не е налична');
                    return;
                }

                // Използваме uploadFiles метода от image-manager.js
                const uploadPromises = files.map(file => this.uploadSingleFile(file));

                Promise.all(uploadPromises)
                    .then(results => {
                        const successfulUploads = results.filter(result => result && result.success);

                        if (successfulUploads.length > 0) {
                            // Добавяме успешно качените изображения към продуктовата форма
                            successfulUploads.forEach(result => {
                                this.addImageToProductForm(result.filename, result.thumb);
                            });

                            this.showAlert('success', `Успешно качени ${successfulUploads.length} изображения`);
                        }
                    })
                    .catch(error => {
                        console.error('Грешка при качване на изображения:', error);
                        this.showAlert('error', 'Възникна грешка при качване на изображенията');
                    });
            }, // End of uploadAndAddImages

            /**
             * Добавя нов елемент за изображение в DOM-а на продуктовата форма
             * @param {string} imagePath - Пътят до пълното изображение
             * @param {string} thumbPath - Пътят до миниатюрата
             */
            addImageToProductForm: function(imagePath, thumbPath) {
                const imagesContainer = document.querySelector('#tab-images .grid');
                if (!imagesContainer) return;

                console.log('addImageToProductForm', imagePath, thumbPath);

                // Намираме бутона за добавяне, за да вмъкнем новото изображение преди него
                const addButton = imagesContainer.querySelector('.border-dashed');

                const imageElement = document.createElement('div');
                imageElement.className = 'relative group';
                imageElement.innerHTML = `
                    <div class="aspect-square rounded-lg overflow-hidden border border-gray-200">
                        <img src="${thumbPath}" alt="Product image" class="w-full h-full object-cover">
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                        <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Избери от библиотеката" data-action="select">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-folder-image-line"></i>
                            </div>
                        </button>
                        <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни" data-action="remove">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-delete-bin-line"></i>
                            </div>
                        </button>
                    </div>
                    <input type="hidden" name="product_image[]" value="${imagePath}">
                `;

                if (addButton) {
                    imagesContainer.insertBefore(imageElement, addButton);
                } else {
                    imagesContainer.appendChild(imageElement);
                }
            },

            /**
             * Намира директорията на последното добавено изображение
             * @returns {string} Пътят до директорията
             */
            getLastImageDirectory: function() {
                const imageInputs = Array.from(document.querySelectorAll('#tab-images input[name*="image"]')).filter(input => input.id !== 'additional-image-upload');
                const lastImageInput = imageInputs[imageInputs.length - 1];

                if (!lastImageInput || !lastImageInput.value) {
                    return '';
                }

                const pathParts = lastImageInput.value.split('/');
                // pathParts.shift(); // Премахва catalog/
                pathParts.pop(); // Премахва името на файла

                return pathParts.join('/');
            },

            /**
             * Инициализация на автозавършването за категории
             */
            initCategoryAutocomplete: function() {
                // Loading indicator за autocomplete
                let loadingIndicator = document.createElement('div');
                const categoryInput = document.getElementById('input-category');
                const categorySuggestions = document.getElementById('product-category-autocomplete');
                const categoryList = document.getElementById('product-category');

                if (!categoryInput || !categorySuggestions || !categoryList) {
                    // console.warn('Елементи за автодовършване на категории не са намерени.');
                    return;
                }

                // Проверяваме дали вече има event listeners
                if (categoryInput.dataset.listenerAttached === 'true') {
                    return;
                }

                loadingIndicator.className = 'autocomplete-loading';
                loadingIndicator.textContent = 'Зареждане...';
                loadingIndicator.style.display = 'none';
                categorySuggestions.parentNode.insertBefore(loadingIndicator, categorySuggestions);

                let categoryDebounceTimer;
                let currentCategoryRequest = null;

                const addCategoryToList = (categoryName, categoryId) => {
                    if (document.getElementById(`product-category-${categoryId}`)) {
                        categoryInput.value = '';
                        categorySuggestions.innerHTML = '';
                        categorySuggestions.classList.add('hidden');
                        loadingIndicator.style.display = 'none';
                        return;
                    }

                    const categoryElement = document.createElement('div');
                    categoryElement.id = `product-category-${categoryId}`;
                    categoryElement.className = 'flex items-center justify-between p-2 bg-gray-100 rounded';
                    categoryElement.innerHTML = `
                        <span class="text-sm">${categoryName}</span>
                        <button type="button" class="text-gray-400 hover:text-red-500 remove-category">
                            <i class="ri-close-line"></i>
                        </button>
                        <input type="hidden" name="product_category[]" value="${categoryId}">
                    `;
                    
                    const removeButton = categoryElement.querySelector('.remove-category');
                    removeButton.addEventListener('click', () => {
                        categoryElement.remove();
                    });
                    
                    categoryList.appendChild(categoryElement);
                    categoryInput.value = '';
                    categorySuggestions.innerHTML = '';
                    categorySuggestions.classList.add('hidden');
                    loadingIndicator.style.display = 'none';
                };

                const fetchAndDisplayCategorySuggestions = (currentQuery) => {
                    loadingIndicator.style.display = 'block';
                    categorySuggestions.classList.add('hidden');

                    if (currentCategoryRequest) {
                        currentCategoryRequest.abort();
                    }
                    const controller = new AbortController();
                    currentCategoryRequest = controller;

                    const urlParams = new URLSearchParams(window.location.search);
                    const userToken = urlParams.get('user_token');
                    const timestamp = new Date().getTime();

                    fetch(`index.php?route=catalog/product/autocomplete&type=category&filter_name=${encodeURIComponent(currentQuery)}&user_token=${userToken}&_=${timestamp}`, {
                        signal: controller.signal,
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Грешка при зареждане на категории');
                        return response.json();
                    })
                    .then(data => {
                        loadingIndicator.style.display = 'none';
                        categorySuggestions.innerHTML = '<div class="autocomplete-suggestions"></div>';
                        const suggestionsList = categorySuggestions.querySelector('.autocomplete-suggestions');

                        if (data.length > 0) {
                            data.forEach(category => {
                                const item = document.createElement('div');
                                item.className = 'autocomplete-suggestion';
                                item.textContent = category.name;
                                item.dataset.id = category.id;
                                item.addEventListener('click', () => {
                                    addCategoryToList(category.name, category.id);
                                });
                                suggestionsList.appendChild(item);
                            });
                            categorySuggestions.classList.remove('hidden');
                        } else {
                            const noResults = document.createElement('div');
                            noResults.className = 'autocomplete-no-results';
                            noResults.textContent = currentQuery ? 'Няма намерени категории' : 'Няма предложения за категории';
                            suggestionsList.appendChild(noResults);
                            categorySuggestions.classList.remove('hidden');
                        }
                    })
                    .catch(error => {
                        loadingIndicator.style.display = 'none';
                        if (error.name !== 'AbortError') {
                            console.error('Грешка при търсене на категории:', error);
                            const suggestionsList = categorySuggestions.querySelector('.autocomplete-suggestions') || categorySuggestions;
                            suggestionsList.innerHTML = '<div class="autocomplete-no-results">Грешка при зареждане.</div>';
                            categorySuggestions.classList.remove('hidden');
                        }
                    });
                };

                categoryInput.addEventListener('input', () => {
                    clearTimeout(categoryDebounceTimer);
                    const query = categoryInput.value.trim();

                    if (query.length === 0) {
                        loadingIndicator.style.display = 'none';
                        categorySuggestions.innerHTML = '';
                        categorySuggestions.classList.add('hidden');
                        if (currentCategoryRequest) {
                            currentCategoryRequest.abort();
                        }
                        return;
                    }
                    
                    categoryDebounceTimer = setTimeout(() => {
                        fetchAndDisplayCategorySuggestions(query);
                    }, 500);
                });

                categoryInput.addEventListener('focus', () => {
                    const query = categoryInput.value.trim();
                    if (query === '' || categorySuggestions.classList.contains('hidden')) {
                        fetchAndDisplayCategorySuggestions(query);
                    }
                });

                categoryInput.addEventListener('keydown', (e) => {
                    const suggestions = categorySuggestions.querySelectorAll('.autocomplete-suggestion');
                    if (suggestions.length === 0 || categorySuggestions.classList.contains('hidden')) return;

                    let activeIndex = Array.from(suggestions).findIndex(s => s.classList.contains('active'));

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex + 1) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex - 1 + suggestions.length) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'Enter' && activeIndex >= 0) {
                        e.preventDefault();
                        suggestions[activeIndex].click();
                    } else if (e.key === 'Escape') {
                        categorySuggestions.innerHTML = '';
                        categorySuggestions.classList.add('hidden');
                    }
                });
                
                document.addEventListener('click', function(event) {
                    if (!categoryInput.contains(event.target) && !categorySuggestions.contains(event.target)) {
                        categorySuggestions.classList.add('hidden');
                    }
                });

                // Маркираме, че event listeners са добавени
                categoryInput.dataset.listenerAttached = 'true';
            },

            initBrandAutocomplete: function() {
                const manufacturerInput = document.getElementById('input-manufacturer');
                const manufacturerIdInput = document.querySelector('input[name="manufacturer_id"]');
                const manufacturerSuggestions = document.getElementById('product-manufacturer-autocomplete');
                const selectedManufacturerContainer = document.getElementById('selected-manufacturer-container');
                const userToken = BackendModule.config.userToken;

                if (!manufacturerInput || !manufacturerSuggestions) {
                    return;
                }

                // Проверяваме дали вече има event listeners
                if (manufacturerInput.dataset.listenerAttached === 'true') {
                    return;
                }

                let brandDebounceTimer;
                let brandAbortController = null;

                function addBrandToList(name, id) {
                    if (!selectedManufacturerContainer) return;

                    // Премахване на съществуващ бадж, ако има такъв (за замяна)
                    const existingBadge = selectedManufacturerContainer.querySelector('.selected-brand-badge');
                    if (existingBadge) {
                        existingBadge.remove();
                    }

                    // Създаване на новия бранд бадж
                    const brandElement = document.createElement('div');
                    brandElement.className = 'selected-brand-badge flex items-center justify-between bg-primary/10 text-primary px-3 py-1.5 rounded-md text-sm my-1';
                    brandElement.innerHTML = `
                        <span>${name}</span>
                        <button type="button" class="remove-selected-manufacturer ml-2 text-red-500 hover:text-red-700" data-id="${id}">
                            <i class="ri-close-line"></i>
                        </button>
                    `;

                    // Event listener за премахване на бранда
                    const removeButton = brandElement.querySelector('.remove-selected-manufacturer');
                    removeButton.addEventListener('click', function() {
                        manufacturerIdInput.value = '';
                        selectedManufacturerContainer.innerHTML = '';
                        manufacturerInput.value = '';
                        manufacturerInput.focus();
                    });

                    // Добавяне на новия бадж
                    selectedManufacturerContainer.appendChild(brandElement);

                    // Скриване на предложенията
                    manufacturerSuggestions.classList.add('hidden');
                    manufacturerSuggestions.innerHTML = '';
                }

                // Обработка на предварително избран производител от сървъра
                if (manufacturerIdInput && manufacturerIdInput.value && selectedManufacturerContainer) {
                    const existingBadge = selectedManufacturerContainer.querySelector('.selected-brand-badge');
                    if (existingBadge) {
                        // Ако вече има рендериран бадж от сървъра, добави event listener за премахване
                        const removeButton = existingBadge.querySelector('.remove-selected-manufacturer');
                        if (removeButton && !removeButton.dataset.listenerAttached) {
                            removeButton.addEventListener('click', function() {
                                manufacturerIdInput.value = '';
                                selectedManufacturerContainer.innerHTML = '';
                                manufacturerInput.value = '';
                                manufacturerInput.focus();
                            });
                            removeButton.dataset.listenerAttached = 'true';
                        }
                    } else if (manufacturerInput.dataset.initialName) {
                        // Fallback: ако има ID и име, но няма рендериран бадж
                        addBrandToList(manufacturerInput.dataset.initialName, manufacturerIdInput.value);
                    }
                }

                function fetchAndDisplayBrandSuggestions(query) {
                    if (brandAbortController) {
                        brandAbortController.abort();
                    }
                    brandAbortController = new AbortController();
                    const signal = brandAbortController.signal;

                    manufacturerSuggestions.innerHTML = '<div class="autocomplete-loading p-2 text-sm text-gray-500">Зареждане...</div>';
                    manufacturerSuggestions.classList.remove('hidden');

                    const timestamp = new Date().getTime();
                    fetch(`index.php?route=catalog/product/autocomplete&type=manufacturer&filter_name=${encodeURIComponent(query)}&user_token=${userToken}&_=${timestamp}`, {
                        signal: signal,
                        headers: { 'Cache-Control': 'no-cache' }
                    })
                    .then(response => {
                        if (signal.aborted) return;
                        if (!response.ok) throw new Error('Грешка при зареждане на марки');
                        return response.json();
                    })
                    .then(data => {
                        if (signal.aborted) return;
                        manufacturerSuggestions.innerHTML = '';
                        if (data.length === 0) {
                            manufacturerSuggestions.innerHTML = '<div class="autocomplete-no-results p-2 text-sm text-gray-500">Няма намерени марки.</div>';
                            return;
                        }
                        const suggestionsContainer = document.createElement('div');
                        suggestionsContainer.className = 'autocomplete-suggestions max-h-60 overflow-y-auto';
                        data.forEach(brand => {
                            const item = document.createElement('div');
                            item.className = 'autocomplete-suggestion p-2 hover:bg-gray-100 cursor-pointer text-sm';
                            item.textContent = brand.name;
                            item.dataset.id = brand.id;
                            item.addEventListener('click', (e) => {
                                e.stopPropagation();
                                manufacturerIdInput.value = brand.id;
                                manufacturerInput.value = '';
                                manufacturerInput.dataset.lastSelectedName = brand.name;
                                addBrandToList(brand.name, brand.id);
                                manufacturerSuggestions.classList.add('hidden');
                            });
                            suggestionsContainer.appendChild(item);
                        });
                        manufacturerSuggestions.appendChild(suggestionsContainer);
                        manufacturerSuggestions.classList.remove('hidden');
                    })
                    .catch(error => {
                        if (signal.aborted) return;
                        console.error('Грешка при автодовършване на марки:', error);
                        manufacturerSuggestions.innerHTML = `<div class="autocomplete-error p-2 text-sm text-red-500">${error.message}</div>`;
                    });
                }

                manufacturerInput.addEventListener('input', () => {
                    if (manufacturerInput.disabled) return;
                    const query = manufacturerInput.value.trim();
                    clearTimeout(brandDebounceTimer);
                    brandDebounceTimer = setTimeout(() => {
                        fetchAndDisplayBrandSuggestions(query);
                    }, 500);
                });

                manufacturerInput.addEventListener('blur', () => {
                    setTimeout(function() {
                        manufacturerInput.value = '';
                    }, 200);
                });

                manufacturerInput.addEventListener('focus', () => {
                    if (manufacturerInput.disabled) return;
                    const query = manufacturerInput.value.trim();
                    if (query === '' || manufacturerSuggestions.classList.contains('hidden') || manufacturerSuggestions.innerHTML === '') {
                        fetchAndDisplayBrandSuggestions(query);
                    }
                });

                manufacturerInput.addEventListener('keydown', (e) => {
                    if (manufacturerInput.disabled) return;
                    const suggestions = manufacturerSuggestions.querySelectorAll('.autocomplete-suggestion');
                    if (suggestions.length === 0 || manufacturerSuggestions.classList.contains('hidden')) return;
                    let activeIndex = Array.from(suggestions).findIndex(s => s.classList.contains('active'));
                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex + 1) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex - 1 + suggestions.length) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'Enter' && activeIndex >= 0) {
                        e.preventDefault();
                        suggestions[activeIndex].click();
                    } else if (e.key === 'Escape') {
                        manufacturerSuggestions.innerHTML = '';
                        manufacturerSuggestions.classList.add('hidden');
                    }
                });
                
                document.addEventListener('click', function(event) {
                    if (!manufacturerInput.contains(event.target) && !manufacturerSuggestions.contains(event.target)) {
                        manufacturerSuggestions.classList.add('hidden');
                    }
                });

                // Маркираме, че event listeners са добавени
                manufacturerInput.dataset.listenerAttached = 'true';
            },

            /**
             * Инициализация на автозавършването за атрибути
             */
            initAttributeAutocomplete: function() {
                // Ще добавим event listeners за всички съществуващи и нови атрибутни полета
                document.addEventListener('input', (e) => {
                    if (e.target.classList.contains('attribute-name-input')) {
                        this.handleAttributeAutocomplete(e.target);
                    }
                });

                document.addEventListener('focusin', (e) => {
                    if (e.target.classList.contains('attribute-name-input')) {
                        this.handleAttributeAutocomplete(e.target);
                    }
                });
            },

            /**
             * Обработка на автозавършването за атрибути
             */
            handleAttributeAutocomplete: function(input) {
                const query = input.value.trim();
                const container = input.nextElementSibling;

                if (!container || !container.classList.contains('attribute-autocomplete-container')) {
                    return;
                }

                // Debounce механизъм
                clearTimeout(input.debounceTimer);

                // При фокусиране или въвеждане винаги правим заявка
                input.debounceTimer = setTimeout(() => {
                    this.fetchAttributeSuggestions(query, container, input);
                }, query.length === 0 ? 100 : 300); // По-бързо за празни полета
            },

            /**
             * Зареждане на предложения за атрибути
             */
            fetchAttributeSuggestions: function(query, container, input) {
                const userToken = BackendModule.config.userToken;
                const timestamp = new Date().getTime();
                const limit = 10; // Лимит на резултатите

                container.innerHTML = '<div class="autocomplete-loading p-2 text-sm text-gray-500">Зареждане...</div>';
                container.classList.remove('hidden');

                // Използваме новия endpoint
                let url = `index.php?route=catalog/product/autocomplete&type=attribute&limit=${limit}&user_token=${userToken}&_=${timestamp}`;
                if (query.length > 0) {
                    url += `&filter_name=${encodeURIComponent(query)}`;
                }

                fetch(url, {
                    headers: { 'Cache-Control': 'no-cache' }
                })
                .then(response => {
                    if (!response.ok) throw new Error('Грешка при зареждане на атрибути');
                    return response.json();
                })
                .then(data => {
                    container.innerHTML = '';

                    if (data.length === 0) {
                        const message = query.length === 0 ? 'Няма налични атрибути.' : 'Няма намерени атрибути.';
                        container.innerHTML = `<div class="autocomplete-no-results p-2 text-sm text-gray-500">${message}</div>`;
                        return;
                    }

                    const suggestionsContainer = document.createElement('div');
                    suggestionsContainer.className = 'autocomplete-suggestions max-h-40 overflow-y-auto border border-gray-300 rounded bg-white shadow-lg';

                    data.forEach(attribute => {
                        const item = document.createElement('div');
                        item.className = 'autocomplete-suggestion p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0';

                        // Показваме името на атрибута и групата (ако има)
                        let displayText = attribute.name;
                        if (attribute.attribute_group) {
                            displayText += ` (${attribute.attribute_group})`;
                        }
                        item.textContent = displayText;

                        item.addEventListener('click', () => {
                            input.value = attribute.name;
                            // Намираме скритото поле за attribute_id
                            const hiddenInput = input.parentElement.querySelector('input[name*="[attribute_id]"]');
                            if (hiddenInput) {
                                hiddenInput.value = attribute.attribute_id;
                            }
                            container.innerHTML = '';
                            container.classList.add('hidden');
                        });

                        suggestionsContainer.appendChild(item);
                    });

                    container.appendChild(suggestionsContainer);
                    container.classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Грешка при автодовършване на атрибути:', error);
                    container.innerHTML = `<div class="autocomplete-error p-2 text-sm text-red-500">${error.message}</div>`;
                });
            },



            /**
             * Инициализация на филтрирането на option values
             */
            initOptionValueFiltering: function() {
                // Добавяме event listener за промяна на типа опция
                document.addEventListener('change', (e) => {
                    if (e.target.classList.contains('option-type-select')) {
                        BackendModule.updateOptionValueSelects(e.target);
                    }
                });

                // Инициализираме филтрирането за съществуващите опции при зареждане
                document.querySelectorAll('.option-type-select[disabled]').forEach(select => {
                   // BackendModule.updateOptionValueSelects(select);
                });
            },

            /**
             * Актуализиране на option value select менютата базирано на типа опция
             */
            updateOptionValueSelects: function(typeSelect) {
                const optionItem = typeSelect.closest('.option-item');
                if (!optionItem) return;

                const valueSelects = optionItem.querySelectorAll('.option-value-select');
                let optionId = null;

                // Проверяваме дали това е съществуваща опция (има disabled атрибут)
                const isExistingOption = typeSelect.hasAttribute('disabled');

                if (isExistingOption) {
                    // За съществуващи опции, намираме option_id по името
                    if (typeSelect.selectedIndex >= 0 && typeSelect.options[typeSelect.selectedIndex]) {
                        const selectedOptionName = typeSelect.options[typeSelect.selectedIndex].text;
                        const matchingOption = BackendModule.findOptionByName(selectedOptionName);
                        optionId = matchingOption ? matchingOption.option_id : null;
                    } else {
                        // Няма избрана опция или невалиден selectedIndex
                        optionId = null;
                    }
                } else {
                    // За нови опции, вземаме option_id от option-select dropdown-а
                    const optionSelect = optionItem.querySelector('.option-select');
                    if (optionSelect && optionSelect.value) {
                        optionId = optionSelect.value;
                    }
                }

                valueSelects.forEach(select => {
                    BackendModule.filterOptionValueSelect(select, optionId);
                });
            },

            /**
             * Намиране на опция по име от dropdown-а
             */
            findOptionByName: function(optionName) {
                if (!window.allOptions || !Array.isArray(window.allOptions)) {
                    return null;
                }

                // Намираме опцията с това име от allOptions
                return window.allOptions.find(option => option.name === optionName);
            },

            /**
             * Филтриране на option value select меню
             */
            filterOptionValueSelect: function(select, optionId) {
                if (!select) return;

                const currentValue = select.value;

                console.log('Filtering option value select. Option ID:', optionId);
                console.log('Current Value:', currentValue);

                // Запазваме първата опция
                const firstOption = '<option value="">Изберете стойност...</option>';

                let optionsHtml = firstOption;

                if (optionId && window.optionValues && Array.isArray(window.optionValues)) {
                    // Филтрираме option values по option_id
                    const filteredValues = window.optionValues.filter(ov => ov.option_id == optionId);

                    console.log('Filtered Values:', filteredValues);

                    if (filteredValues.length > 0) {
                        filteredValues.forEach(optionValue => {
                            const selected = currentValue == optionValue.option_value_id ? 'selected' : '';
                            optionsHtml += `<option value="${optionValue.option_value_id}" data-option-id="${optionValue.option_id}" ${selected}>${optionValue.name}</option>`;
                        });
                    } else {
                        optionsHtml += '<option value="" disabled>Няма налични стойности за този тип опция</option>';
                    }
                } else {

                    console.log('No option ID provided. Showing all option values.');

                    // Ако няма optionId, показваме всички option values
                    if (window.optionValues && Array.isArray(window.optionValues)) {
                        window.optionValues.forEach(optionValue => {
                            const selected = currentValue == optionValue.option_value_id ? 'selected' : '';
                            optionsHtml += `<option value="${optionValue.option_value_id}" data-option-id="${optionValue.option_id}" ${selected}>${optionValue.name}</option>`;
                        });
                    }
                }

                select.innerHTML = optionsHtml;
            },

            /**
             * Инициализация на функционалността за атрибути
             */
            initAttributes: function() {
                const addAttributeBtn = document.getElementById('add-attribute');
                const attributesContainer = document.getElementById('attributes-container');

                if (!addAttributeBtn || !attributesContainer) {
                    return;
                }

                // Проверяваме дали вече има event listener
                if (addAttributeBtn.dataset.listenerAttached === 'true') {
                    return;
                }

                addAttributeBtn.addEventListener('click', (e) => {
                    e.preventDefault();

                    // Премахваме съобщението за липса на атрибути, ако съществува
                    const emptyMessage = document.getElementById('no-attributes');
                    if (emptyMessage) {
                        emptyMessage.remove();
                    }

                    const attributeIndex = document.querySelectorAll('.attribute-item').length;

                    // Генериране на многоезични полета с по-стабилна проверка
                    let languages = [];

                    // Проверяваме дали window.languages е валиден масив
                    if (window.languages && Array.isArray(window.languages) && window.languages.length > 0) {
                        languages = window.languages;
                    } else {
                        // Fallback към активния език
                        languages = [{
                            language_id: window.activeLanguageId || 1,
                            code: 'BG',
                            name: 'Български'
                        }];
                    }

                    let languageFields = '';
                    languages.forEach(language => {
                        languageFields += `
                            <div class="flex items-center space-x-3">
                                <div class="w-20 h-6 flex items-center justify-center bg-gray-100 rounded text-xs font-medium text-gray-600">
                                    ${language.name}
                                </div>
                                <input type="text" name="product_attribute[${attributeIndex}][product_attribute_description][${language.language_id}][text]" class="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Стойност на ${language.name}" value="">
                            </div>
                        `;
                    });

                    const attributeHtml = `
                        <div class="attribute-item border border-gray-200 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Име на атрибута</label>
                                    <input type="text" name="product_attribute[${attributeIndex}][name]" class="attribute-name-input w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Започнете да пишете за търсене на атрибут..." value="">
                                    <div class="attribute-autocomplete-container mt-1 relative" style="z-index: 1000;">
                                        <!-- Autocomplete suggestions will be populated here -->
                                    </div>
                                    <input type="hidden" name="product_attribute[${attributeIndex}][attribute_id]" value="">
                                </div>
                                <div class="flex items-end justify-end">
                                    <button type="button" class="remove-attribute w-8 h-8 flex items-center justify-center bg-red-50 text-red-500 border border-red-200 rounded hover:bg-red-100 transition-colors">
                                        <i class="ri-delete-bin-line text-sm"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="border-t border-gray-200 pt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Стойности по езици</label>
                                <div class="space-y-3">
                                    ${languageFields}
                                </div>
                            </div>
                        </div>
                    `;

                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = attributeHtml.trim();
                    attributesContainer.appendChild(tempDiv.firstChild);
                });

                // Маркираме, че event listener е добавен
                addAttributeBtn.dataset.listenerAttached = 'true';
            },

            /**
             * Инициализация на функционалността за опции
             */
            initOptions: function() {
                const addOptionBtn = document.getElementById('add-option');
                const optionsContainer = document.getElementById('options-container');

                if (!addOptionBtn || !optionsContainer) {
                    return;
                }

                // Проверяваме дали вече има event listener
                if (addOptionBtn.dataset.listenerAttached === 'true') {
                    return;
                }

                addOptionBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    // Премахваме съобщението за липса на опции, ако съществува
                    const emptyMessage = document.getElementById('no-options');
                    if (emptyMessage) {
                        emptyMessage.remove();
                    }

                    const optionIndex = document.querySelectorAll('.option-item').length;

                    // Използваме само активния език за опростяване
                    const activeLanguageId = window.activeLanguageId;

                    // Генериране на опции за dropdown
                    let optionOptionsHtml = '<option value="">Изберете опция...</option>';
                    if (window.allOptions && Array.isArray(window.allOptions)) {
                        window.allOptions.forEach(option => {
                            optionOptionsHtml += `<option value="${option.option_id}" data-type="${option.type}">${option.name}</option>`;
                        });
                    }

                    const optionHtml = `
                        <div class="option-item border border-gray-200 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Опция</label>
                                    <select name="product_option[${optionIndex}][option_id]" class="option-select w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                                        ${optionOptionsHtml}
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Тип опция</label>
                                    <select name="product_option[${optionIndex}][type]" class="option-type-select w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                                        <option value="">Изберете първо опция</option>
                                        <option value="select" selected="">Избор от списък</option>
                                        <option value="radio">Радио бутони</option>
                                        <option value="checkbox">Отметки</option>
                                        <option value="text">Текстово поле</option>
                                        <option value="textarea">Текстова област</option>
                                        <option value="file">Файл</option>
                                        <option value="date">Дата</option>
                                        <option value="time">Време</option>
                                        <option value="datetime">Дата и време</option>
                                    </select>
                                </div>
                                <div class="flex items-end justify-end">
                                    <div class="flex items-center space-x-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Задължителна</label>
                                            <div class="flex items-center space-x-2">
                                                <label class="toggle-switch">
                                                    <input type="checkbox" name="product_option[${optionIndex}][required]" value="1">
                                                    <span class="toggle-slider"></span>
                                                </label>
                                                <span class="text-sm text-gray-700">Да</span>
                                            </div>
                                        </div>
                                        <button type="button" class="remove-option w-8 h-8 flex items-center justify-center bg-red-50 text-red-500 border border-red-200 rounded hover:bg-red-100 transition-colors">
                                            <i class="ri-delete-bin-line text-sm"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="border-t border-gray-200 pt-4 mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Име на опцията</label>
                                <input type="text" name="product_option[${optionIndex}][product_option_description][${activeLanguageId}][name]" class="option-name-input w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Ще се попълни автоматично" value="" readonly>
                            </div>
                            <div class="option-values-section" style="display: block;">
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="flex justify-between items-center mb-3">
                                        <label class="block text-sm font-medium text-gray-700">Стойности на опцията</label>
                                        <button type="button" class="add-option-value px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-xs">
                                            <i class="ri-add-line mr-1"></i>Добави стойност
                                        </button>
                                    </div>
                                    <div class="option-values-container space-y-2">
                                        <!-- Стойностите ще се добавят динамично -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = optionHtml.trim();
                    const newOption = tempDiv.firstChild;
                    optionsContainer.appendChild(newOption);

                    // Добавяме event listeners за новата опция
                    const optionSelect = newOption.querySelector('.option-select');
                    const typeSelect = newOption.querySelector('.option-type-select');
                    const nameInput = newOption.querySelector('.option-name-input');
                    const valuesSection = newOption.querySelector('.option-values-section');

                    // Event listener за промяна на опцията
                    optionSelect.addEventListener('change', function() {
                        const selectedOption = window.allOptions.find(opt => opt.option_id == this.value);

                        if (selectedOption) {
                            // Попълваме типа и името
                            typeSelect.value = selectedOption.type;
                            nameInput.value = selectedOption.name;

                            // Показваме/скриваме секцията за стойности
                            const showValues = ['select', 'radio', 'checkbox'].includes(selectedOption.type);
                            valuesSection.style.display = showValues ? 'block' : 'none';

                            // Актуализираме option value select менютата
                            BackendModule.updateOptionValueSelects(typeSelect);
                        } else {
                            // Изчистваме полетата
                            typeSelect.value = '';
                            nameInput.value = '';
                            valuesSection.style.display = 'none';
                        }
                    });

                    // Event listener за промяна на типа опция (за съществуващи опции)
                    typeSelect.addEventListener('change', function() {
                        const showValues = ['select', 'radio', 'checkbox'].includes(this.value);
                        valuesSection.style.display = showValues ? 'block' : 'none';

                        // Актуализираме option value select менютата при промяна на типа
                        BackendModule.updateOptionValueSelects(this);
                    });
                });

                // Маркираме, че event listener е добавен
                addOptionBtn.dataset.listenerAttached = 'true';
            },

            /**
             * Инициализация на функционалността за статуса на продукта
             */
            initProductStatus: function() {
                const statusCheckbox = document.querySelector('input[name="status"]');
                const statusLabel = document.querySelector('input[name="status"]').closest('.toggle-switch').nextElementSibling;

                if (statusCheckbox && statusLabel) {
                    // Функция за актуализиране на текста на етикета
                    const updateStatusLabel = () => {
                        if (statusCheckbox.checked) {
                            statusLabel.textContent = 'Активен';
                            statusLabel.classList.remove('text-red-600');
                            statusLabel.classList.add('text-green-600');
                        } else {
                            statusLabel.textContent = 'Неактивен';
                            statusLabel.classList.remove('text-green-600');
                            statusLabel.classList.add('text-red-600');
                        }
                    };

                    // Инициализиране на правилния статус при зареждане
                    updateStatusLabel();

                    // Event listener за промяна на статуса
                    statusCheckbox.addEventListener('change', updateStatusLabel);
                }
            },

            /**
             * Инициализация на езиковите табове
             */
            initLanguageTabs: function() {
                // Основни езикови табове
                const languageTabs = document.querySelectorAll('.language-tab');
                const languageContents = document.querySelectorAll('.language-content');

                languageTabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        const languageId = this.getAttribute('data-language');

                        // Активиране на таба
                        languageTabs.forEach(t => {
                            t.classList.remove('border-primary', 'text-primary', 'active');
                            t.classList.add('border-transparent', 'text-gray-500');
                        });
                        this.classList.add('border-primary', 'text-primary', 'active');
                        this.classList.remove('border-transparent', 'text-gray-500');

                        // Показване на съдържанието
                        languageContents.forEach(content => {
                            if (content.getAttribute('data-language') === languageId) {
                                content.classList.remove('hidden');
                            } else {
                                content.classList.add('hidden');
                            }
                        });

                        // Показване на съответното съдържание за тагове
                        const tagContents = document.querySelectorAll('.language-content-tags');
                        tagContents.forEach(content => {
                            if (content.getAttribute('data-language') === languageId) {
                                content.classList.remove('hidden');
                            } else {
                                content.classList.add('hidden');
                            }
                        });
                    });
                });

                // Описание езикови табове
                const descriptionTabs = document.querySelectorAll('.description-language-tab');
                const descriptionContents = document.querySelectorAll('.description-language-content');

                descriptionTabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        const languageId = this.getAttribute('data-language');

                        descriptionTabs.forEach(t => {
                            t.classList.remove('border-primary', 'text-primary', 'active');
                            t.classList.add('border-transparent', 'text-gray-500');
                        });
                        this.classList.add('border-primary', 'text-primary', 'active');
                        this.classList.remove('border-transparent', 'text-gray-500');

                        descriptionContents.forEach(content => {
                            if (content.getAttribute('data-language') === languageId) {
                                content.classList.remove('hidden');
                            } else {
                                content.classList.add('hidden');
                            }
                        });
                    });
                });

                // SEO езикови табове
                const seoTabs = document.querySelectorAll('.seo-language-tab');
                const seoContents = document.querySelectorAll('.seo-language-content');

                seoTabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        const languageId = this.getAttribute('data-language');

                        seoTabs.forEach(t => {
                            t.classList.remove('border-primary', 'text-primary', 'active');
                            t.classList.add('border-transparent', 'text-gray-500');
                        });
                        this.classList.add('border-primary', 'text-primary', 'active');
                        this.classList.remove('border-transparent', 'text-gray-500');

                        seoContents.forEach(content => {
                            if (content.getAttribute('data-language') === languageId) {
                                content.classList.remove('hidden');
                            } else {
                                content.classList.add('hidden');
                            }
                        });
                    });
                });
            },


            /**
             * Показване на съобщение
             * @param {string} type Тип на съобщението (success, error, info)
             * @param {string} message Съдържание на съобщението
             * @param {number} duration Продължителност в секунди (0 за постоянно съобщение)
             */
            showAlert: function(type, message, duration = 3) {
                // Премахване на съществуващи съобщения
                const existingAlert = document.querySelector('.alert-message');
                if (existingAlert) {
                    existingAlert.remove();
                }
                
                // Създаване на новото съобщение
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert-message fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                    type === 'success' ? 'bg-green-100 text-green-800 border-l-4 border-green-500' :
                    type === 'error' ? 'bg-red-100 text-red-800 border-l-4 border-red-500' :
                    'bg-blue-100 text-blue-800 border-l-4 border-blue-500'
                } flex items-center space-x-3`;
                
                // Икона според типа
                const iconClass = 
                    type === 'success' ? 'ri-check-line' :
                    type === 'error' ? 'ri-error-warning-line' :
                    'ri-information-line';
                
                alertDiv.innerHTML = `
                    <div class="flex-shrink-0">
                        <i class="${iconClass}"></i>
                    </div>
                    <div>${message}</div>
                    <button class="ml-auto text-gray-500 hover:text-gray-700" id="close-alert">
                        <i class="ri-close-line"></i>
                    </button>
                `;
                
                document.body.appendChild(alertDiv);
                
                // Бутон за затваряне
                const closeButton = alertDiv.querySelector('#close-alert');
                closeButton.addEventListener('click', () => alertDiv.remove());
                
                // Автоматично затваряне след определено време
                if (duration > 0) {
                    setTimeout(() => {
                        if (document.body.contains(alertDiv)) {
                            alertDiv.remove();
                        }
                    }, duration * 1000);
                }
            },

            /**
             * Инициализация на функционалността за свързани продукти
             */
            initRelatedProducts: function() {
                const relatedProductInput = document.getElementById('input-related-product');
                const relatedProductSuggestions = document.getElementById('product-related-autocomplete');
                const relatedProductsList = document.getElementById('product-related-list');
                const noRelatedProductsMessage = document.getElementById('no-related-products');
                const userToken = BackendModule.config.userToken;

                if (!relatedProductInput || !relatedProductSuggestions || !relatedProductsList) {
                    return;
                }

                // Проверяваме дали вече има event listeners
                if (relatedProductInput.dataset.listenerAttached === 'true') {
                    return;
                }

                let relatedDebounceTimer;
                let relatedAbortController = null;

                // Получаване на текущия product_id от формата
                const getCurrentProductId = () => {
                    const productIdInput = document.querySelector('input[name="product_id"]');
                    return productIdInput ? productIdInput.value : 0;
                };

                // Добавяне на продукт към списъка със свързани продукти
                const addRelatedProduct = (product) => {
                    // Проверка дали продуктът вече е добавен
                    if (document.getElementById(`product-related-${product.product_id}`)) {
                        relatedProductInput.value = '';
                        relatedProductSuggestions.innerHTML = '';
                        relatedProductSuggestions.classList.add('hidden');
                        return;
                    }

                    // Скриване на съобщението за липса на продукти
                    if (noRelatedProductsMessage) {
                        noRelatedProductsMessage.style.display = 'none';
                    }

                    // Създаване на елемента за продукта
                    const productElement = document.createElement('div');
                    productElement.id = `product-related-${product.product_id}`;
                    productElement.className = 'flex items-center justify-between p-3 bg-gray-100 rounded-lg';
                    productElement.innerHTML = `
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gray-200 rounded mr-3 flex-shrink-0">
                                ${product.thumb ?
                                    `<img src="${product.thumb}" alt="${product.name}" class="w-full h-full object-cover rounded">` :
                                    `<div class="w-full h-full flex items-center justify-center text-gray-400">
                                        <i class="ri-image-line"></i>
                                    </div>`
                                }
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">${product.name}</div>
                                <div class="text-xs text-gray-500">Код: ${product.model}</div>
                            </div>
                        </div>
                        <button type="button" class="text-gray-400 hover:text-red-500 remove-related-product" data-product-id="${product.product_id}">
                            <i class="ri-close-line"></i>
                        </button>
                        <input type="hidden" name="product_related[]" value="${product.product_id}">
                    `;

                    // Event listener за премахване ще се обработи от глобалния listener

                    relatedProductsList.appendChild(productElement);
                    relatedProductInput.value = '';
                    relatedProductSuggestions.innerHTML = '';
                    relatedProductSuggestions.classList.add('hidden');
                };

                // Търсене и показване на предложения за продукти
                const fetchAndDisplayProductSuggestions = (query) => {
                    if (relatedAbortController) {
                        relatedAbortController.abort();
                    }
                    relatedAbortController = new AbortController();
                    const signal = relatedAbortController.signal;

                    relatedProductSuggestions.innerHTML = '<div class="autocomplete-loading p-2 text-sm text-gray-500">Зареждане...</div>';
                    relatedProductSuggestions.classList.remove('hidden');

                    const currentProductId = getCurrentProductId();
                    const timestamp = new Date().getTime();

                    fetch(`index.php?route=catalog/product/autocomplete&type=product&filter_name=${encodeURIComponent(query)}&exclude_product_id=${currentProductId}&user_token=${userToken}&_=${timestamp}`, {
                        signal: signal,
                        headers: { 'Cache-Control': 'no-cache' }
                    })
                    .then(response => {
                        if (signal.aborted) return;
                        if (!response.ok) throw new Error('Грешка при зареждане на продукти');
                        return response.json();
                    })
                    .then(data => {
                        if (signal.aborted) return;
                        relatedProductSuggestions.innerHTML = '';

                        if (data.length === 0) {
                            relatedProductSuggestions.innerHTML = '<div class="autocomplete-no-results p-2 text-sm text-gray-500">Няма намерени продукти.</div>';
                            return;
                        }

                        const suggestionsContainer = document.createElement('div');
                        suggestionsContainer.className = 'autocomplete-suggestions max-h-60 overflow-y-auto';

                        data.forEach(product => {
                            const item = document.createElement('div');
                            item.className = 'autocomplete-suggestion p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0';
                            item.innerHTML = `
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-200 rounded mr-3 flex-shrink-0">
                                        ${product.thumb ?
                                            `<img src="${product.thumb}" alt="${product.name}" class="w-full h-full object-cover rounded">` :
                                            `<div class="w-full h-full flex items-center justify-center text-gray-400">
                                                <i class="ri-image-line text-xs"></i>
                                            </div>`
                                        }
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900">${product.name}</div>
                                        <div class="text-xs text-gray-500">Код: ${product.model} | Цена: ${product.price} лв.</div>
                                    </div>
                                </div>
                            `;

                            item.addEventListener('click', (e) => {
                                e.stopPropagation();
                                addRelatedProduct(product);
                            });

                            suggestionsContainer.appendChild(item);
                        });

                        relatedProductSuggestions.appendChild(suggestionsContainer);
                        relatedProductSuggestions.classList.remove('hidden');
                    })
                    .catch(error => {
                        if (signal.aborted) return;
                        console.error('Грешка при автодовършване на продукти:', error);
                        relatedProductSuggestions.innerHTML = `<div class="autocomplete-error p-2 text-sm text-red-500">${error.message}</div>`;
                    });
                };

                // Event listeners
                relatedProductInput.addEventListener('input', () => {
                    const query = relatedProductInput.value.trim();
                    clearTimeout(relatedDebounceTimer);

                    if (query.length === 0) {
                        relatedProductSuggestions.innerHTML = '';
                        relatedProductSuggestions.classList.add('hidden');
                        if (relatedAbortController) {
                            relatedAbortController.abort();
                        }
                        return;
                    }

                    relatedDebounceTimer = setTimeout(() => {
                        fetchAndDisplayProductSuggestions(query);
                    }, 500);
                });

                relatedProductInput.addEventListener('focus', () => {
                    const query = relatedProductInput.value.trim();
                    if (query.length > 0 && relatedProductSuggestions.classList.contains('hidden')) {
                        fetchAndDisplayProductSuggestions(query);
                    }
                });

                relatedProductInput.addEventListener('keydown', (e) => {
                    const suggestions = relatedProductSuggestions.querySelectorAll('.autocomplete-suggestion');
                    if (suggestions.length === 0 || relatedProductSuggestions.classList.contains('hidden')) return;

                    let activeIndex = Array.from(suggestions).findIndex(s => s.classList.contains('active'));

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex + 1) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex - 1 + suggestions.length) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'Enter' && activeIndex >= 0) {
                        e.preventDefault();
                        suggestions[activeIndex].click();
                    } else if (e.key === 'Escape') {
                        relatedProductSuggestions.innerHTML = '';
                        relatedProductSuggestions.classList.add('hidden');
                    }
                });

                // Скриване на предложенията при клик извън тях
                document.addEventListener('click', function(event) {
                    if (!relatedProductInput.contains(event.target) && !relatedProductSuggestions.contains(event.target)) {
                        relatedProductSuggestions.classList.add('hidden');
                    }
                });

                // Маркираме, че event listeners са добавени
                relatedProductInput.dataset.listenerAttached = 'true';
            },

            /**
             * Инициализация на функционалността за управление на промоционални цени
             */
            initSpecialPricesManagement: function() {
                const addSpecialPriceBtn = document.getElementById('add-special-price');
                const specialPricesContainer = document.getElementById('special-prices-container');
                var noSpecialPricesMessage = document.getElementById('no-special-prices');


                if (!addSpecialPriceBtn || !specialPricesContainer) {

                    console.log('Elements not found!');
                    return;
                }

                // Проверяваме дали вече има event listener
                if (addSpecialPriceBtn.dataset.listenerAttached === 'true') {
                    return;
                }

                // Добавяне на нова промоционална цена
                addSpecialPriceBtn.addEventListener('click', () => {
                    if(!noSpecialPricesMessage) noSpecialPricesMessage = document.getElementById('no-special-prices');

                    // Премахваме съобщението за липса на промоционални цени, ако съществува
                    if (noSpecialPricesMessage) {
                        noSpecialPricesMessage.remove();
                    }


                    const specialPriceIndex = document.querySelectorAll('.special-price-item').length;
                    const specialPriceHtml = `
                        <div class="special-price-item grid grid-cols-1 md:grid-cols-4 gap-3 p-3 border border-gray-200 rounded-lg">
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">Цена (лв.)</label>
                                <div class="relative">
                                    <input type="number" name="product_special[${specialPriceIndex}][price]" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="0.00" value="" onchange="this.value = parseFloat(this.value || 0).toFixed(2)">
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <span class="text-gray-500 text-xs">лв.</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">От дата</label>
                                <input type="date" name="product_special[${specialPriceIndex}][date_start]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" value="">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">До дата</label>
                                <input type="date" name="product_special[${specialPriceIndex}][date_end]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" value="">
                            </div>
                            <div class="flex items-end">
                                <button type="button" class="remove-special-price w-8 h-8 flex items-center justify-center bg-red-50 text-red-500 border border-red-200 rounded hover:bg-red-100 transition-colors">
                                    <i class="ri-delete-bin-line text-sm"></i>
                                </button>
                            </div>
                        </div>
                    `;

                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = specialPriceHtml.trim();
                    specialPricesContainer.appendChild(tempDiv.firstChild);
                });

                // Маркираме, че event listener е добавен
                addSpecialPriceBtn.dataset.listenerAttached = 'true';
            },


            /**
             * Инициализира обработката на изпращането на формата
             */
            initFormSubmission: function() {
                // Намираме бутона за запазване по атрибута form="product-form"
                const button = document.querySelector('button[type="submit"][form="product-form"]');
                if (!button) {
                    console.error('Бутонът за запазване не е намерен!');
                    return;
                }

                button.addEventListener('click', (e) => {
                    // Спираме изпращането на формата
                    e.preventDefault();

                    const form = document.getElementById('product-form');
                    const formData = new FormData(form);
                    const self = this; // Запазваме референция към this

                    // Премахваме ненужни полета
                    ['image', 'related_product_search', 'category_name'].forEach(field => {
                        formData.delete(field);
                    });

                    // Валидация на данните за опции
                    this.validateOptionData();

                    // Дебъг информация за опциите (може да се премахне в продукция)
                    if (console && console.log) {
                        this.logOptionData(formData);
                    }

                    
                    // Показваме индикатор за зареждане
                    const originalButtonText = button.innerHTML;
                    button.disabled = true;
                    button.innerHTML = '<i class="ri-loader-4-line ri-spin"></i> Запазване...';
                    
                    // Изпращаме AJAX заявка
                    fetch('index.php?route=catalog/product/save&user_token=' + BackendModule.config.userToken, {
                        method: 'POST',
                        body: formData,
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(function(response) {
                        if (!response.ok) {
                            throw new Error(`Грешка при изпращане на заявката: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(function(result) {
                        if (result.success) {
                            // Показваме съобщение за успех
                            self.showAlert('success', result.success, 5);
                            
                            // Актуализираме URL с product_id ако има такъв
                            if (result.product_id && !formData.get('product_id')) {
                                const currentUrl = new URL(window.location.href);
                                currentUrl.searchParams.set('product_id', result.product_id);
                                window.history.pushState({}, '', currentUrl);
                                
                                // Актуализираме скритото поле product_id във формата
                                const productIdInput = form.querySelector('input[name="product_id"]');
                                if (productIdInput) {
                                    productIdInput.value = result.product_id;
                                }
                            }
                        } else if (result.error) {
                            self.showAlert('error', `Грешка: ${result.error}`, 5);
                        }
                    })
                    .catch(function(error) {
                        console.error('Грешка при изпращане на формата:', error);
                        self.showAlert('error', 'Възникна грешка при запазване на формата. Моля, опитайте отново.', 5);
                    })
                    .finally(function() {
                        // Връщаме бутона в първоначално състояние
                        button.disabled = false;
                        button.innerHTML = originalButtonText;
                    });
                });
            }



        });

        // Извикване на основната инициализираща функция
        // BackendModule.initProductForm();

        // Глобални event listeners (извън инициализацията за да не се дублират)
        document.addEventListener('click', function(e) {
            // Премахване на промоционална цена
            if (e.target.closest('.remove-special-price')) {
                const specialPriceItem = e.target.closest('.special-price-item');
                if (specialPriceItem && confirm('Сигурни ли сте, че искате да премахнете тази промоционална цена?')) {
                    specialPriceItem.remove();

                    // // Показване на съобщението за липса на промоционални цени, ако няма други
                    // const specialPricesContainer = document.getElementById('special-prices-container');
                    // const noSpecialPricesMessage = document.getElementById('no-special-prices');
                    // const remainingSpecialPrices = specialPricesContainer ? specialPricesContainer.querySelectorAll('.special-price-item') : [];
                    // if (remainingSpecialPrices.length === 0 && noSpecialPricesMessage) {
                    //     noSpecialPricesMessage.style.display = 'block';
                    // }

                    // Проверяваме дали има останали промоционални цени
                    const specialPricesContainer = document.getElementById('special-prices-container');
                    const remainingSpecials = specialPricesContainer.querySelectorAll('.special-price-item');
                    if (remainingSpecials.length === 0) {
                        // Добавяме съобщението за липса на промоции
                        const emptyMessage = document.createElement('div');
                        emptyMessage.id = 'no-special-prices';
                        emptyMessage.className = 'text-gray-500 text-sm italic text-center py-3 border border-dashed border-gray-300 rounded-lg';
                        emptyMessage.textContent = 'Няма добавени промоционални цени. Използвайте бутона "Добави промоция" за да добавите нова промоционална цена.';
                        specialPricesContainer.appendChild(emptyMessage);
                    }
                }
            }

            // Премахване на атрибут
            if (e.target.closest('.remove-attribute')) {
                const attributeItem = e.target.closest('.attribute-item');
                if (attributeItem && confirm('Сигурни ли сте, че искате да премахнете този атрибут?')) {
                    attributeItem.remove();

                    // Проверяваме дали има останали атрибути
                    const attributesContainer = document.getElementById('attributes-container');
                    const remainingAttributes = attributesContainer.querySelectorAll('.attribute-item');
                    if (remainingAttributes.length === 0) {
                        // Добавяме съобщението за липса на атрибути
                        const emptyMessage = document.createElement('div');
                        emptyMessage.id = 'no-attributes';
                        emptyMessage.className = 'text-gray-500 text-sm italic text-center py-3 border border-dashed border-gray-300 rounded-lg';
                        emptyMessage.textContent = 'Няма добавени атрибути. Използвайте бутона "Добави атрибут" за да добавите нов атрибут.';
                        attributesContainer.appendChild(emptyMessage);
                    }
                }
            }

            // Премахване на опция
            if (e.target.closest('.remove-option')) {
                const optionItem = e.target.closest('.option-item');
                if (optionItem && confirm('Сигурни ли сте, че искате да премахнете тази опция?')) {
                    optionItem.remove();

                    // Проверяваме дали има останали опции
                    const optionsContainer = document.getElementById('options-container');
                    const remainingOptions = optionsContainer.querySelectorAll('.option-item');
                    if (remainingOptions.length === 0) {
                        // Добавяме съобщението за липса на опции
                        const emptyMessage = document.createElement('div');
                        emptyMessage.id = 'no-options';
                        emptyMessage.className = 'text-gray-500 text-sm italic text-center py-3 border border-dashed border-gray-300 rounded-lg';
                        emptyMessage.textContent = 'Няма добавени опции. Използвайте бутона "Добави опция" за да добавите нова опция.';
                        optionsContainer.appendChild(emptyMessage);
                    }
                }
            }

            // Добавяне на стойност към опция
            if (e.target.closest('.add-option-value')) {
                const optionItem = e.target.closest('.option-item');
                const valuesContainer = optionItem.querySelector('.option-values-container');
                const optionIndex = Array.from(document.querySelectorAll('.option-item')).indexOf(optionItem);
                const valueIndex = valuesContainer.querySelectorAll('.option-value-item').length;

                // Генерираме option values от глобалните данни
                let optionValuesHtml = '<option value="">Изберете стойност...</option>';
                if (window.optionValues && Array.isArray(window.optionValues)) {
                    window.optionValues.forEach(optionValue => {
                        optionValuesHtml += `<option value="${optionValue.option_value_id}" data-option-id="${optionValue.option_id}">${optionValue.name}</option>`;
                    });
                }

                const valueHtml = `
                    <div class="option-value-item flex items-end gap-3 p-3 bg-gray-50 rounded">
                        <div class="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">Стойност</label>
                                <select name="product_option[${optionIndex}][product_option_value][${valueIndex}][option_value_id]" class="option-value-select w-full px-2 py-1 border border-gray-300 rounded text-xs">
                                    ${optionValuesHtml}
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">Количество</label>
                                <input type="number" name="product_option[${optionIndex}][product_option_value][${valueIndex}][quantity]" class="w-full px-2 py-1 border border-gray-300 rounded text-xs" value="0">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">Цена</label>
                                <input type="number" name="product_option[${optionIndex}][product_option_value][${valueIndex}][price]" step="0.01" class="w-full px-2 py-1 border border-gray-300 rounded text-xs" value="0.00" onchange="this.value = parseFloat(this.value || 0).toFixed(2)">
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <button type="button" class="remove-option-value w-6 h-6 flex items-center justify-center bg-red-50 text-red-500 border border-red-200 rounded hover:bg-red-100 transition-colors">
                                <i class="ri-close-line text-xs"></i>
                            </button>
                        </div>
                    </div>
                `;

                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = valueHtml.trim();
                const newValueElement = tempDiv.firstChild;
                valuesContainer.appendChild(newValueElement);

                // Филтрираме option values за новия select базирано на типа опция
                const typeSelect = optionItem.querySelector('.option-type-select');
                const optionIDhiidenInput = optionItem.querySelector('input[name*="[option_id]"]');
                if (optionIDhiidenInput) {
                    const newSelect = newValueElement.querySelector('.option-value-select');
                    let optionId = null;

                    // Проверяваме дали това е съществуваща опция (има disabled атрибут)
                    const isExistingOption = typeSelect.hasAttribute('disabled');

                    if (isExistingOption) {
                        // // За съществуващи опции, намираме option_id по името
                        // const selectedOptionName = typeSelect.options[typeSelect.selectedIndex].text;
                        // const matchingOption = BackendModule.findOptionByName(selectedOptionName);
                        // optionId = matchingOption ? matchingOption.option_id : null;

                        // Вземаме option_id от скритото поле
                        optionId = optionIDhiidenInput.value;
                    } else {
                        // За нови опции, вземаме option_id от option-select dropdown-а
                        const optionSelect = optionItem.querySelector('.option-select');
                        if (optionSelect && optionSelect.value) {
                            optionId = optionSelect.value;
                        }
                    }

                    BackendModule.filterOptionValueSelect(newSelect, optionId);
                }
            }

            // Премахване на стойност от опция
            if (e.target.closest('.remove-option-value')) {
                const valueItem = e.target.closest('.option-value-item');
                if (valueItem && confirm('Сигурни ли сте, че искате да премахнете тази стойност?')) {
                    valueItem.remove();
                }
            }

            // Премахване на свързан продукт
            if (e.target.closest('.remove-related-product')) {
                const productElement = e.target.closest('[id^="product-related-"]');
                if (productElement) {
                    productElement.remove();

                    // Показване на съобщението за липса на продукти, ако няма други
                    const relatedProductsList = document.getElementById('product-related-list');
                    const noRelatedProductsMessage = document.getElementById('no-related-products');
                    const remainingProducts = relatedProductsList ? relatedProductsList.querySelectorAll('[id^="product-related-"]') : [];
                    if (remainingProducts.length === 0 && noRelatedProductsMessage) {
                        noRelatedProductsMessage.style.display = 'block';
                    }
                }
            }
        });

        // Event listener за промяна на типа опция
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('option-type-select')) {
                const optionItem = e.target.closest('.option-item');
                const valuesSection = optionItem.querySelector('.option-values-section');
                const showValues = ['select', 'radio', 'checkbox'].includes(e.target.value);
                valuesSection.style.display = showValues ? 'block' : 'none';
            }
        });

        // Добавяне на методи за валидация и логиране на данни за опции
        Object.assign(BackendModule, {
            /**
             * Валидация на данните за опции
             */
            validateOptionData: function() {
                const optionItems = document.querySelectorAll('.option-item');
                let hasErrors = false;

                optionItems.forEach((item, index) => {
                    const typeSelect = item.querySelector('.option-type-select');
                    const optionType = typeSelect ? typeSelect.value : '';

                    // Проверяваме дали има тип на опцията
                    if (!optionType) {
                        console.warn(`Опция ${index + 1}: Липсва тип на опцията`);
                        hasErrors = true;
                    }

                    // За опции с избор проверяваме дали има стойности
                    if (['select', 'radio', 'checkbox', 'image'].includes(optionType)) {
                        const valueItems = item.querySelectorAll('.option-value-item');
                        if (valueItems.length === 0) {
                            console.warn(`Опция ${index + 1}: Липсват стойности за опция от тип "${optionType}"`);
                            hasErrors = true;
                        }
                    }
                });

                if (hasErrors) {
                    console.warn('Открити са проблеми с данните за опциите. Моля, проверете конзолата за детайли.');
                }
            },

            /**
             * Логиране на данните за опции за дебъг
             * @param {FormData} formData - Данните от формата
             */
            logOptionData: function(formData) {
                const optionData = {};

                // Събираме всички данни за опции
                for (let [key, value] of formData.entries()) {
                    if (key.startsWith('product_option[')) {
                        optionData[key] = value;
                    }
                }

                if (Object.keys(optionData).length > 0) {
                    console.group('Данни за опции на продукта:');
                    Object.entries(optionData).forEach(([key, value]) => {
                        console.log(`${key}: ${value}`);
                    });
                    console.groupEnd();
                }
            }
        });
    } // Край на if (typeof BackendModule !== 'undefined')

})();
